import csv from 'csv-parser';
import { Readable } from 'stream';
import  logger  from './logger';

export interface CSVRecord {
  name: string;
  phone: string;
  email: string;
  row?: number;
}

export interface ValidationError {
  row: number;
  field: string;
  value: string;
  reason: string;
}

export interface ProcessingResult {
  validRecords: CSVRecord[];
  errors: ValidationError[];
  totalRows: number;
  processedRows: number;
  skippedRows: number;
}

export class CSVProcessor {
  private maxRecords: number;
  private requiredColumns = ['name', 'phone', 'email'];

  constructor(maxRecords: number = 100000) {
    this.maxRecords = maxRecords;
  }

  /**
   * Process CSV buffer and return validated records
   */
  async processCSV(buffer: Buffer): Promise<ProcessingResult> {
    return new Promise((resolve, reject) => {
      const validRecords: CSVRecord[] = [];
      const errors: ValidationError[] = [];
      let totalRows = 0;
      let processedRows = 0;
      let skippedRows = 0;
      let headerValidated = false;

      const stream = Readable.from(buffer);

      stream
        .pipe(csv({
          mapHeaders: ({ header }) => header.toLowerCase().trim()
        }))
        .on('headers', (headers: string[]) => {
          logger.info(`CSV headers detected: ${headers.join(', ')}`);
          
          // Validate required columns
          const missingColumns = this.requiredColumns.filter(
            col => !headers.includes(col)
          );
          
          if (missingColumns.length > 0) {
            return reject(new Error(
              `Missing required columns: ${missingColumns.join(', ')}. ` +
              `Required columns are: ${this.requiredColumns.join(', ')}`
            ));
          }
          
          headerValidated = true;
        })
        .on('data', (row: any) => {
          totalRows++;
          
          if (!headerValidated) {
            return;
          }

          // Check record limit
          if (totalRows > this.maxRecords) {
            errors.push({
              row: totalRows,
              field: 'general',
              value: '',
              reason: `Exceeded maximum record limit of ${this.maxRecords}`
            });
            skippedRows++;
            return;
          }

          const validationErrors = this.validateRecord(row, totalRows);
          
          if (validationErrors.length > 0) {
            errors.push(...validationErrors);
            skippedRows++;
          } else {
            validRecords.push({
              name: row.name.trim(),
              phone: this.normalizePhoneNumber(row.phone),
              email: row.email.trim().toLowerCase(),
              row: totalRows
            });
            processedRows++;
          }
        })
        .on('end', () => {
          logger.info(`CSV processing completed: ${processedRows} valid, ${skippedRows} skipped, ${errors.length} errors`);
          
          resolve({
            validRecords,
            errors,
            totalRows,
            processedRows,
            skippedRows
          });
        })
        .on('error', (error) => {
          logger.error('CSV parsing error:', error);
          reject(new Error(`CSV parsing failed: ${error.message}`));
        });
    });
  }

  /**
   * Validate individual CSV record
   */
  private validateRecord(row: any, rowNumber: number): ValidationError[] {
    const errors: ValidationError[] = [];

    // Validate name
    if (!row.name || typeof row.name !== 'string' || row.name.trim().length === 0) {
      errors.push({
        row: rowNumber,
        field: 'name',
        value: row.name || '',
        reason: 'Name is required and cannot be empty'
      });
    } else if (row.name.trim().length > 100) {
      errors.push({
        row: rowNumber,
        field: 'name',
        value: row.name,
        reason: 'Name cannot exceed 100 characters'
      });
    }

    // Validate phone
    if (!row.phone || typeof row.phone !== 'string') {
      errors.push({
        row: rowNumber,
        field: 'phone',
        value: row.phone || '',
        reason: 'Phone number is required'
      });
    } else if (!this.isValidPhoneNumber(row.phone)) {
      errors.push({
        row: rowNumber,
        field: 'phone',
        value: row.phone,
        reason: 'Invalid phone number format. Must be 10+ digits, can include country codes'
      });
    }

    // Validate email
    if (!row.email || typeof row.email !== 'string') {
      errors.push({
        row: rowNumber,
        field: 'email',
        value: row.email || '',
        reason: 'Email is required'
      });
    } else if (!this.isValidEmail(row.email)) {
      errors.push({
        row: rowNumber,
        field: 'email',
        value: row.email,
        reason: 'Invalid email format'
      });
    }

    return errors;
  }

  /**
   * Validate phone number format
   */
  private isValidPhoneNumber(phone: string): boolean {
    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '');
    
    // Check for valid patterns:
    // - Must have at least 10 digits
    // - Can start with + for country code
    // - Total length should be reasonable (10-15 digits)
    const phoneRegex = /^\+?[\d]{10,15}$/;
    
    return phoneRegex.test(cleaned);
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  /**
   * Normalize phone number to consistent format
   */
  private normalizePhoneNumber(phone: string): string {
    // Remove all non-digit characters except +
    let cleaned = phone.replace(/[^\d+]/g, '');
    
    // If no country code and starts with valid digits, assume it's a local number
    if (!cleaned.startsWith('+') && cleaned.length >= 10) {
      // For Indian numbers starting with 6-9, add +91
      if (/^[6-9]/.test(cleaned) && cleaned.length === 10) {
        cleaned = '+91' + cleaned;
      }
      // For US numbers (10 digits), add +1
      else if (cleaned.length === 10) {
        cleaned = '+1' + cleaned;
      }
      // For other cases, add + if not present
      else if (!cleaned.startsWith('+')) {
        cleaned = '+' + cleaned;
      }
    }
    
    return cleaned;
  }

  /**
   * Get processing statistics
   */
  getProcessingStats(result: ProcessingResult) {
    return {
      totalRecords: result.totalRows,
      processedRecords: result.processedRows,
      skippedRecords: result.skippedRows,
      errorCount: result.errors.length,
      successRate: result.totalRows > 0 ? 
        Math.round((result.processedRows / result.totalRows) * 100) : 0
    };
  }
}

export default CSVProcessor;

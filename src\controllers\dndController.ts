import { Request, Response } from 'express';
import { CSVProcessor } from '../utils/csvProcessor';
import DNDService from '../services/dndService';
import { config } from '../config';
import logger from '../utils/logger';

export class DNDController {
  private csvProcessor: CSVProcessor;
  private dndService: DNDService;

  constructor() {
    this.csvProcessor = new CSVProcessor(config.dnd.maxRecordsLimit);
    this.dndService = new DNDService();
  }

  /**
   * Handle bulk DND validation via CSV upload
   * POST /api/dnd/upload
   */
  async uploadAndValidate(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    
    try {
      logger.info('Starting DND bulk validation process');
      
      // Validate file upload
      if (!req.file) {
        res.status(400).json({
          success: false,
          error: 'No file uploaded. Please provide a CSV file using the "csvFile" field'
        });
        return;
      }

      logger.info(`Processing CSV file: ${req.file.originalname}, size: ${req.file.size} bytes`);

      // Step 1: Process CSV file
      const csvResult = await this.csvProcessor.processCSV(req.file.buffer);
      
      if (csvResult.validRecords.length === 0) {
        res.status(400).json({
          success: false,
          error: 'No valid records found in CSV file',
          errors: csvResult.errors,
          summary: {
            totalRecords: csvResult.totalRows,
            processedRecords: 0,
            skippedRecords: csvResult.skippedRows,
            dndCount: 0,
            nonDndCount: 0,
            errorCount: csvResult.errors.length
          }
        });
        return;
      }

      logger.info(`CSV processing completed: ${csvResult.validRecords.length} valid records`);

      // Step 2: Perform DND validation
      const dndResult = await this.dndService.processBulkDND(csvResult.validRecords);

      // Step 3: Combine results and prepare response
      const processingTime = Date.now() - startTime;
      
      const response = {
        success: true,
        summary: {
          totalRecords: csvResult.totalRows,
          processedRecords: csvResult.processedRows,
          skippedRecords: csvResult.skippedRows,
          dndCount: dndResult.summary.dndCount,
          nonDndCount: dndResult.summary.nonDndCount,
          errorCount: dndResult.summary.errorCount + csvResult.errors.length
        },
        results: dndResult.results,
        errors: csvResult.errors.map(error => ({
          row: error.row,
          reason: `${error.field}: ${error.reason}`
        })),
        metadata: {
          processingTimeMs: processingTime,
          fileName: req.file.originalname,
          fileSize: req.file.size,
          timestamp: new Date().toISOString()
        }
      };

      logger.info(`DND validation completed in ${processingTime}ms: ${dndResult.summary.dndCount} DND, ${dndResult.summary.nonDndCount} Non-DND`);

      res.status(200).json(response);

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('DND validation failed:', error);

      res.status(500).json({
        success: false,
        error: (error as Error).message || 'Internal server error during DND validation',
        metadata: {
          processingTimeMs: processingTime,
          fileName: req.file?.originalname,
          fileSize: req.file?.size,
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  /**
   * Get DND service health status
   * GET /api/dnd/health
   */
  async getHealthStatus(req: Request, res: Response): Promise<void> {
    try {
      const isHealthy = await this.dndService.healthCheck();
      
      res.status(200).json({
        success: true,
        service: 'DND Validation Service',
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        configuration: {
          maxFileSize: `${config.dnd.maxFileSizeMB}MB`,
          maxRecords: config.dnd.maxRecordsLimit,
          maxConcurrentRequests: config.dnd.maxConcurrentRequests,
          retryAttempts: config.dnd.retryAttempts,
          timeout: `${config.dnd.timeout}ms`
        }
      });

    } catch (error) {
      logger.error('Health check failed:', error);
      
      res.status(500).json({
        success: false,
        service: 'DND Validation Service',
        status: 'error',
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get service configuration and limits
   * GET /api/dnd/config
   */
  async getConfiguration(req: Request, res: Response): Promise<void> {
    try {
      res.status(200).json({
        success: true,
        configuration: {
          upload: {
            maxFileSize: `${config.dnd.maxFileSizeMB}MB`,
            maxRecords: config.dnd.maxRecordsLimit,
            allowedFormats: ['CSV'],
            requiredColumns: ['name', 'phone', 'email']
          },
          processing: {
            maxConcurrentRequests: config.dnd.maxConcurrentRequests,
            retryAttempts: config.dnd.retryAttempts,
            retryDelay: `${config.dnd.retryDelayMs}ms`,
            timeout: `${config.dnd.timeout}ms`
          },
          endpoints: {
            upload: '/api/dnd/upload',
            health: '/api/dnd/health',
            config: '/api/dnd/config'
          }
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Failed to get configuration:', error);
      
      res.status(500).json({
        success: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Validate single phone number (for testing)
   * POST /api/dnd/validate-single
   */
  async validateSingle(req: Request, res: Response): Promise<void> {
    try {
      const { phone } = req.body;

      if (!phone) {
        res.status(400).json({
          success: false,
          error: 'Phone number is required'
        });
        return;
      }

      // Basic phone validation
      const phoneRegex = /^\+?[\d\s\-\(\)]{10,15}$/;
      if (!phoneRegex.test(phone)) {
        res.status(400).json({
          success: false,
          error: 'Invalid phone number format'
        });
        return;
      }

      logger.info(`Single DND validation for: ${phone}`);

      // Create a mock record for processing
      const mockRecord = {
        name: 'Test User',
        phone: phone,
        email: '<EMAIL>'
      };

      const result = await this.dndService.processBulkDND([mockRecord]);

      res.status(200).json({
        success: true,
        phone: phone,
        dnd_status: result.results[0]?.dnd_status || 'Error',
        error_message: result.results[0]?.error_message,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Single DND validation failed:', error);
      
      res.status(500).json({
        success: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      });
    }
  }
}

export default DNDController;

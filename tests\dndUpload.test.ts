import request from 'supertest';
import fs from 'fs';
import path from 'path';
import nock from 'nock';
import app from '../src/server';
import { config } from '../src/config';

// Clear any existing axios mocks for this test file
jest.unmock('axios');

describe('DND Upload API', () => {
  const baseUrl = '/api/v1/dnd';

  beforeEach(() => {
    // Clean all nock interceptors before each test
    nock.cleanAll();
  });

  afterEach(() => {
    // Clean all nock interceptors after each test
    nock.cleanAll();
  });

  afterAll(() => {
    // Restore nock after all tests
    nock.restore();
  });
  
  beforeAll(() => {
    // Disable console output during tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'info').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  beforeEach(() => {
    // Clear all nock interceptors before each test
    nock.cleanAll();
  });

  afterAll(() => {
    // Restore console methods
    jest.restoreAllMocks();
    nock.cleanAll();
  });

  describe('POST /upload', () => {
    describe('Happy Path', () => {
      it('should successfully process valid CSV file', async () => {
        // Mock msgclub API responses
        nock(config.dnd.apiBaseUrl)
          .get(config.dnd.apiEndpoint)
          .query(true) // Accept any query parameters
          .reply(200, {
            "Wrong Number": [],
            "Non-NDNC": [1234567890, 9876543210, 7700900123, 412345678, 123456789],
            "NDNC": [],
            "response": "success"
          });

        const csvPath = path.join(__dirname, 'fixtures', 'sample-valid.csv');
        
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.summary).toMatchObject({
          totalRecords: 5,
          processedRecords: 5,
          skippedRecords: 0,
          dndCount: 0,
          nonDndCount: 5,
          errorCount: 0
        });
        expect(response.body.results).toHaveLength(5);
        expect(response.body.results[0]).toMatchObject({
          name: 'John Doe',
          phone: '+1234567890',
          email: '<EMAIL>',
          dnd_status: 'Non-DND'
        });
      });

      it('should handle mixed DND statuses correctly', async () => {
        // Mock msgclub API with mixed DND statuses
        nock(config.dnd.apiBaseUrl)
          .get(config.dnd.apiEndpoint)
          .query(true)
          .reply(200, {
            "Wrong Number": [123456789], // +33123456789 -> invalid for Indian mobile
            "Non-NDNC": [9876543210, 412345678], // +************, +61412345678 -> Non-DND
            "NDNC": [1234567890, 7700900123], // +1234567890, +************ -> DND
            "response": "success"
          });

        const csvPath = path.join(__dirname, 'fixtures', 'sample-valid.csv');
        
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.summary.dndCount).toBeGreaterThan(0);
        expect(response.body.summary.nonDndCount).toBeGreaterThan(0);
      });

      it('should handle phone number normalization correctly', async () => {
        // Mock msgclub API response for normalized phone numbers
        nock(config.dnd.apiBaseUrl)
          .get(config.dnd.apiEndpoint)
          .query(true)
          .reply(200, {
            "Wrong Number": [],
            "Non-NDNC": [9876543210], // Only Indian mobile numbers are processed
            "NDNC": [],
            "response": "success"
          });

        const csvPath = path.join(__dirname, 'fixtures', 'sample-valid.csv');

        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        expect(response.body.success).toBe(true);
        // Most numbers will be marked as errors due to phone normalization
        expect(response.body.summary.errorCount).toBeGreaterThan(0);
        expect(response.body.summary.nonDndCount).toBeGreaterThan(0);
      });
    });

    describe('File Validation', () => {
      it('should reject non-CSV files', async () => {
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', Buffer.from('test content'), 'test.txt')
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('Only CSV files are allowed');
      });

      it('should reject files without csvFile field', async () => {
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('wrongField', Buffer.from('name,phone,email\n'), 'test.csv')
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('Unexpected file field');
      });

      it('should reject empty files', async () => {
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', Buffer.from(''), 'empty.csv')
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('empty');
      });

      it('should reject files with missing required columns', async () => {
        const csvPath = path.join(__dirname, 'fixtures', 'sample-missing-columns.csv');
        
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('Missing required columns');
      });
    });

    describe('Data Validation', () => {
      it('should handle invalid records and provide detailed errors', async () => {
        const csvPath = path.join(__dirname, 'fixtures', 'sample-invalid.csv');
        
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('No valid records found');
        expect(response.body.errors).toHaveLength(5); // All records are invalid
        expect(response.body.errors[0]).toMatchObject({
          row: expect.any(Number),
          reason: expect.stringContaining('Name is required')
        });
      });

      it('should process mixed valid/invalid records', async () => {
        // Mock DND API for valid records
        nock(config.dnd.apiBaseUrl)
          .get(config.dnd.singleEndpoint)
          .query(true)
          .times(3)
          .reply(200, { dnd_status: 'Non-DND' });

        const csvPath = path.join(__dirname, 'fixtures', 'sample-mixed.csv');
        
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.summary.processedRecords).toBe(3); // 3 valid records
        expect(response.body.summary.skippedRecords).toBe(3); // 3 invalid records
        expect(response.body.errors).toHaveLength(3);
      });
    });

    describe('Error Handling', () => {
      it('should handle DND API failures gracefully', async () => {
        // Mock DND API failures
        nock(config.dnd.apiBaseUrl)
          .get(config.dnd.singleEndpoint)
          .query(true)
          .times(5)
          .reply(500, { error: 'Internal server error' });

        const csvPath = path.join(__dirname, 'fixtures', 'sample-valid.csv');
        
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.summary.errorCount).toBe(5);
        expect(response.body.results[0].dnd_status).toBe('Error');
        expect(response.body.results[0].error_message).toBeDefined();
      });

      it('should handle msgclub API timeouts', async () => {
        // Mock msgclub API timeout
        nock(config.dnd.apiBaseUrl)
          .get(config.dnd.apiEndpoint)
          .query(true)
          .delayConnection(config.dnd.timeout + 1000)
          .reply(200, {
            "Wrong Number": [],
            "Non-NDNC": [9876543210],
            "NDNC": [],
            "response": "success"
          });

        const csvPath = path.join(__dirname, 'fixtures', 'sample-valid.csv');
        
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.summary.errorCount).toBe(5);
      });

      it('should retry failed requests', async () => {
        // Mock first request failure, second success
        nock(config.dnd.apiBaseUrl)
          .get(config.dnd.singleEndpoint)
          .query({ phone: '+1234567890' })
          .reply(500, { error: 'Server error' })
          .get(config.dnd.singleEndpoint)
          .query({ phone: '+1234567890' })
          .reply(200, { dnd_status: 'Non-DND' });

        const csvContent = 'name,phone,email\nJohn Doe,+1234567890,<EMAIL>';
        
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', Buffer.from(csvContent), 'test.csv')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.summary.errorCount).toBe(0);
        expect(response.body.results[0].dnd_status).toBe('Non-DND');
      });
    });

    describe('Performance', () => {
      it('should process large files within reasonable time', async () => {
        // Mock DND API responses for large file
        nock(config.dnd.apiBaseUrl)
          .get(config.dnd.singleEndpoint)
          .query(true)
          .times(20)
          .reply(200, { dnd_status: 'Non-DND' });

        const csvPath = path.join(__dirname, 'fixtures', 'sample-large.csv');
        const startTime = Date.now();
        
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        const processingTime = Date.now() - startTime;
        
        expect(response.body.success).toBe(true);
        expect(response.body.summary.processedRecords).toBe(20);
        expect(processingTime).toBeLessThan(30000); // Should complete within 30 seconds
      });

      it('should process records in batches', async () => {
        let batchCount = 0;

        // Mock msgclub API with batch tracking
        nock(config.dnd.apiBaseUrl)
          .get(config.dnd.apiEndpoint)
          .query(true)
          .reply(function() {
            batchCount++;

            // Return success response for batch
            return [200, {
              "Wrong Number": [],
              "Non-NDNC": [9876543210, 9876543211, 9876543212, **********, **********],
              "NDNC": [],
              "response": "success"
            }];
          })
          .persist(); // Allow multiple calls

        const csvPath = path.join(__dirname, 'fixtures', 'sample-large.csv');

        await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        // Should have made at least one batch call
        expect(batchCount).toBeGreaterThan(0);
      });
    });
  });

  describe('GET /health', () => {
    it('should return service health status', async () => {
      // Mock msgclub API health check
      nock(config.dnd.apiBaseUrl)
        .get(config.dnd.apiEndpoint)
        .query(true)
        .reply(200, {
          "Wrong Number": [],
          "Non-NDNC": [],
          "NDNC": [**********],
          "response": "success"
        });

      const response = await request(app)
        .get(`${baseUrl}/health`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.service).toBe('DND Validation Service');
      expect(response.body.status).toBeDefined();
      expect(response.body.configuration).toBeDefined();
    });
  });

  describe('GET /config', () => {
    it('should return service configuration', async () => {
      const response = await request(app)
        .get(`${baseUrl}/config`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.configuration).toMatchObject({
        upload: {
          maxFileSize: expect.stringContaining('MB'),
          maxRecords: expect.any(Number),
          allowedFormats: ['CSV'],
          requiredColumns: ['name', 'phone', 'email']
        },
        processing: {
          maxConcurrentRequests: expect.any(Number),
          retryAttempts: expect.any(Number)
        }
      });
    });
  });

  describe('POST /validate-single', () => {
    it('should validate single phone number', async () => {
      // Mock msgclub API response for Indian mobile number
      nock(config.dnd.apiBaseUrl)
        .get(config.dnd.apiEndpoint)
        .query(true)
        .reply(200, {
          "Wrong Number": [],
          "Non-NDNC": [],
          "NDNC": [9876543210],
          "response": "success"
        });

      const response = await request(app)
        .post(`${baseUrl}/validate-single`)
        .send({ phone: '+************' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.phone).toBe('+************');
      expect(response.body.dnd_status).toBe('DND');
    });

    it('should reject invalid phone numbers', async () => {
      const response = await request(app)
        .post(`${baseUrl}/validate-single`)
        .send({ phone: 'invalid' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid phone number format');
    });

    it('should require phone number', async () => {
      const response = await request(app)
        .post(`${baseUrl}/validate-single`)
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Phone number is required');
    });
  });
});

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { config } from '../config';
import logger from '../utils/logger';
import { CSVRecord } from '../utils/csvProcessor';
import {
  normalizePhoneNumbers,
  batchPhoneNumbers,
  createPhoneNumberString,
  normalizePhoneNumber
} from '../utils/phoneNormalizer';

export interface DNDCheckResult {
  name: string;
  phone: string;
  email: string;
  dnd_status: 'DND' | 'Non-DND' | 'Error';
  error_message?: string;
}

export interface BulkDNDResponse {
  success: boolean;
  results: DNDCheckResult[];
  summary: {
    totalRecords: number;
    processedRecords: number;
    dndCount: number;
    nonDndCount: number;
    errorCount: number;
  };
}

export interface DNDAPIResponse {
  phone: string;
  dnd_status: 'DND' | 'Non-DND';
  registry?: string;
  last_updated?: string;
}

export interface MsgClubAPIResponse {
  "Wrong Number": number[];
  "Non-NDNC": number[];
  "NDNC": number[];
  "response": string;
}

export class DNDService {
  private axiosInstance: AxiosInstance;
  private retryAttempts: number;
  private retryDelay: number;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: config.dnd.apiBaseUrl,
      timeout: config.dnd.timeout,
      headers: {
        'User-Agent': 'TeleAI-DND-Checker/1.0'
      }
    });

    this.retryAttempts = config.dnd.retryAttempts;
    this.retryDelay = config.dnd.retryDelayMs;

    // Add request/response interceptors for logging
    this.setupInterceptors();
  }

  /**
   * Process bulk DND validation for CSV records using msgclub API
   */
  async processBulkDND(records: CSVRecord[]): Promise<BulkDNDResponse> {
    logger.info(`Starting msgclub DND processing for ${records.length} records`);

    try {
      // Use msgclub batch processing
      return await this.processMsgClubBatches(records);

    } catch (error) {
      logger.error('msgclub DND processing failed:', error);
      throw new Error(`DND processing failed: ${(error as Error).message}`);
    }
  }

  /**
   * Process records using msgclub API in batches
   */
  private async processMsgClubBatches(records: CSVRecord[]): Promise<BulkDNDResponse> {
    logger.info(`Processing ${records.length} records using msgclub API`);

    const results: DNDCheckResult[] = [];
    let dndCount = 0;
    let nonDndCount = 0;
    let errorCount = 0;

    // First, normalize all phone numbers and separate valid/invalid
    const phoneNumbers = records.map(record => record.phone);
    const { valid: validPhones, invalid: invalidPhones } = normalizePhoneNumbers(phoneNumbers);

    // Add invalid phone numbers as errors
    invalidPhones.forEach(({ phone, error }) => {
      const record = records.find(r => r.phone === phone);
      if (record) {
        results.push({
          name: record.name,
          phone: record.phone,
          email: record.email,
          dnd_status: 'Error',
          error_message: error
        });
        errorCount++;
      }
    });

    // Process valid phone numbers in batches
    if (validPhones.length > 0) {
      const batches = batchPhoneNumbers(validPhones, config.dnd.maxBatchSize);
      logger.info(`Processing ${validPhones.length} valid numbers in ${batches.length} batches`);

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        if (!batch) continue;
        logger.info(`Processing batch ${i + 1}/${batches.length} with ${batch.length} numbers`);

        try {
          const batchResults = await this.processMsgClubBatch(batch, records);
          results.push(...batchResults.results);
          dndCount += batchResults.dndCount;
          nonDndCount += batchResults.nonDndCount;
          errorCount += batchResults.errorCount;

          // Add delay between batches to respect rate limits
          if (i < batches.length - 1) {
            await this.sleep(1000); // 1 second delay between batches
          }
        } catch (error) {
          logger.error(`Batch ${i + 1} processing failed:`, error);

          // Mark all numbers in this batch as errors
          batch.forEach(phone => {
            const record = records.find(r => normalizePhoneNumber(r.phone).normalized === phone);
            if (record) {
              results.push({
                name: record.name,
                phone: record.phone,
                email: record.email,
                dnd_status: 'Error',
                error_message: `Batch processing failed: ${(error as Error).message}`
              });
              errorCount++;
            }
          });
        }
      }
    }

    logger.info(`msgclub processing completed: ${dndCount} DND, ${nonDndCount} Non-DND, ${errorCount} errors`);

    return {
      success: true,
      results,
      summary: {
        totalRecords: records.length,
        processedRecords: results.length,
        dndCount,
        nonDndCount,
        errorCount
      }
    };
  }

  /**
   * Process a single batch using msgclub API
   */
  private async processMsgClubBatch(
    phoneNumbers: string[],
    allRecords: CSVRecord[]
  ): Promise<{
    results: DNDCheckResult[];
    dndCount: number;
    nonDndCount: number;
    errorCount: number;
  }> {
    const results: DNDCheckResult[] = [];
    let dndCount = 0;
    let nonDndCount = 0;
    let errorCount = 0;

    let lastError: Error | null = null;

    // Retry logic for msgclub API call
    for (let attempt = 1; attempt <= this.retryAttempts + 1; attempt++) {
      try {
        logger.debug(`msgclub API attempt ${attempt} for ${phoneNumbers.length} numbers`);

        const phoneNumberString = createPhoneNumberString(phoneNumbers);
        const response = await this.axiosInstance.get(config.dnd.apiEndpoint, {
          params: {
            AUTH_KEY: config.dnd.authKey,
            mobileNumbers: phoneNumberString
          }
        });

        if (response.data && response.data.response === 'success') {
          const msgClubResponse: MsgClubAPIResponse = response.data;

          // Process each phone number and map to results
          phoneNumbers.forEach(phone => {
            const phoneAsNumber = parseInt(phone, 10);
            const record = allRecords.find(r => normalizePhoneNumber(r.phone).normalized === phone);

            if (!record) {
              logger.warn(`No record found for phone number: ${phone}`);
              return;
            }

            let dndStatus: 'DND' | 'Non-DND' | 'Error';
            let errorMessage: string | undefined;

            if (msgClubResponse["Wrong Number"].includes(phoneAsNumber)) {
              dndStatus = 'Error';
              errorMessage = 'Invalid phone number';
              errorCount++;
            } else if (msgClubResponse["NDNC"].includes(phoneAsNumber)) {
              dndStatus = 'DND';
              dndCount++;
            } else if (msgClubResponse["Non-NDNC"].includes(phoneAsNumber)) {
              dndStatus = 'Non-DND';
              nonDndCount++;
            } else {
              dndStatus = 'Error';
              errorMessage = 'Phone number not found in response';
              errorCount++;
            }

            const result: DNDCheckResult = {
              name: record.name,
              phone: record.phone,
              email: record.email,
              dnd_status: dndStatus
            };

            if (errorMessage) {
              result.error_message = errorMessage;
            }

            results.push(result);
          });

          logger.debug(`msgclub batch processed: ${dndCount} DND, ${nonDndCount} Non-DND, ${errorCount} errors`);
          return { results, dndCount, nonDndCount, errorCount };
        }

        throw new Error('Invalid response from msgclub API');

      } catch (error: any) {
        lastError = error;

        if (attempt <= this.retryAttempts) {
          const delay = this.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
          logger.warn(`msgclub API call failed, retrying in ${delay}ms (attempt ${attempt}):`, error.message);
          await this.sleep(delay);
        }
      }
    }

    logger.error(`msgclub API call failed after ${this.retryAttempts + 1} attempts:`, lastError);
    throw lastError || new Error('msgclub API call failed');
  }

  /**
   * Check single phone number DND status using msgclub API
   */
  async checkSingleDND(phoneNumber: string): Promise<DNDAPIResponse> {
    logger.info(`Checking single DND status for: ${phoneNumber}`);

    // Normalize phone number
    const normalizedResult = normalizePhoneNumber(phoneNumber);
    if (!normalizedResult.isValid) {
      throw new Error(normalizedResult.error || 'Invalid phone number format');
    }

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.retryAttempts + 1; attempt++) {
      try {
        logger.debug(`msgclub single DND check attempt ${attempt} for ${phoneNumber}`);

        const response = await this.axiosInstance.get(config.dnd.apiEndpoint, {
          params: {
            AUTH_KEY: config.dnd.authKey,
            mobileNumbers: normalizedResult.normalized
          }
        });

        if (response.data && response.data.response === 'success') {
          const msgClubResponse: MsgClubAPIResponse = response.data;
          const phoneAsNumber = parseInt(normalizedResult.normalized, 10);

          let dndStatus: 'DND' | 'Non-DND';

          if (msgClubResponse["Wrong Number"].includes(phoneAsNumber)) {
            throw new Error('Invalid phone number');
          } else if (msgClubResponse["NDNC"].includes(phoneAsNumber)) {
            dndStatus = 'DND';
          } else if (msgClubResponse["Non-NDNC"].includes(phoneAsNumber)) {
            dndStatus = 'Non-DND';
          } else {
            throw new Error('Phone number not found in response');
          }

          return {
            phone: phoneNumber,
            dnd_status: dndStatus,
            registry: 'msgclub'
          };
        }

        throw new Error('Invalid response from msgclub API');

      } catch (error: any) {
        lastError = error;

        if (attempt <= this.retryAttempts) {
          const delay = this.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
          logger.warn(`Single DND check failed for ${phoneNumber}, retrying in ${delay}ms (attempt ${attempt})`);
          await this.sleep(delay);
        }
      }
    }

    logger.error(`Single DND check failed for ${phoneNumber} after ${this.retryAttempts + 1} attempts:`, lastError);
    throw lastError || new Error('DND check failed');
  }



  /**
   * Setup axios interceptors for logging and monitoring
   */
  private setupInterceptors(): void {
    this.axiosInstance.interceptors.request.use(
      (config) => {
        logger.debug(`DND API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('DND API Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        logger.debug(`DND API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        if (error.response) {
          logger.error(`DND API Error: ${error.response.status} ${error.response.statusText}`, {
            url: error.config?.url,
            method: error.config?.method,
            params: error.config?.params,
            headers: error.config?.headers,
            data: error.response.data
          });
        } else if (error.request) {
          logger.error('DND API Network Error - No response received:', {
            url: error.config?.url,
            method: error.config?.method,
            message: error.message
          });
        } else {
          logger.error('DND API Request Setup Error:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Utility function for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Health check for msgclub DND service
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Test with a dummy phone number to check if the service is responsive
      const response = await this.axiosInstance.get(config.dnd.apiEndpoint, {
        params: {
          AUTH_KEY: config.dnd.authKey,
          mobileNumbers: '**********' // Test number
        },
        timeout: 5000
      });

      // Check if we get a valid response structure
      return response.status === 200 &&
             response.data &&
             typeof response.data.response === 'string';
    } catch (error) {
      logger.warn('msgclub DND service health check failed:', (error as Error).message);
      return false;
    }
  }
}

export default DNDService;

import axios from 'axios';
import FormData from 'form-data';
import { Readable } from 'stream';
import logger from '../../utils/logger';

export interface TranscriptionResult {
  text: string;
  confidence: number;
  language?: string;
  duration?: number;
}

export interface AudioProcessorConfig {
  sttProvider: 'openai' | 'deepgram' | 'elevenlabs' | 'mock';
  ttsProvider: 'openai' | 'elevenlabs' | 'mock';
  openaiApiKey?: string | undefined;
  deepgramApiKey?: string | undefined;
  elevenlabsApiKey?: string | undefined;
  voice?: string;
  language?: string;
}

export class AudioProcessor {
  private config: AudioProcessorConfig;

  constructor(config: AudioProcessorConfig) {
    this.config = config;
    logger.info(`🎙️ Audio processor initialized with STT: ${config.sttProvider}, TTS: ${config.ttsProvider}`);
  }

  async speechToText(audioBuffer: Buffer): Promise<TranscriptionResult | null> {
    try {
      switch (this.config.sttProvider) {
        case 'openai':
          return await this.openaiSTT(audioBuffer);
        case 'deepgram':
          return await this.deepgramSTT(audioBuffer);
        case 'elevenlabs':
          return await this.elevenlabsSTT(audioBuffer);
        case 'mock':
          return await this.mockSTT(audioBuffer);
        default:
          throw new Error(`Unsupported STT provider: ${this.config.sttProvider}`);
      }
    } catch (error) {
      logger.error('❌ Speech-to-text error:', error);
      return null;
    }
  }

  async textToSpeech(text: string): Promise<Buffer> {
    try {
      switch (this.config.ttsProvider) {
        case 'openai':
          return await this.openaiTTS(text);
        case 'elevenlabs':
          return await this.elevenlabsTTS(text);
        case 'mock':
          return await this.mockTTS(text);
        default:
          throw new Error(`Unsupported TTS provider: ${this.config.ttsProvider}`);
      }
    } catch (error) {
      logger.error('❌ Text-to-speech error:', error);
      // Return silence buffer as fallback
      return this.generateSilence(1000); // 1 second of silence
    }
  }

  // OpenAI Whisper STT
  private async openaiSTT(audioBuffer: Buffer): Promise<TranscriptionResult | null> {
    if (!this.config.openaiApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      // Convert PCM to WAV format for OpenAI
      const wavBuffer = this.pcmToWav(audioBuffer);
      
      const formData = new FormData();
      formData.append('file', wavBuffer, {
        filename: 'audio.wav',
        contentType: 'audio/wav'
      });
      formData.append('model', 'whisper-1');
      formData.append('language', this.config.language || 'en');
      formData.append('response_format', 'verbose_json');

      const response = await axios.post(
        'https://api.openai.com/v1/audio/transcriptions',
        formData,
        {
          headers: {
            'Authorization': `Bearer ${this.config.openaiApiKey}`,
            ...formData.getHeaders()
          },
          timeout: 10000
        }
      );

      const result = response.data;
      
      if (result.text && result.text.trim()) {
        return {
          text: result.text.trim(),
          confidence: 0.9, // OpenAI doesn't provide confidence scores
          language: result.language,
          duration: result.duration
        };
      }

      return null;

    } catch (error) {
      logger.error('❌ OpenAI STT error:', error);
      return null;
    }
  }

  // Deepgram STT
  private async deepgramSTT(audioBuffer: Buffer): Promise<TranscriptionResult | null> {
    if (!this.config.deepgramApiKey) {
      throw new Error('Deepgram API key not configured');
    }

    try {
      const response = await axios.post(
        'https://api.deepgram.com/v1/listen',
        audioBuffer,
        {
          headers: {
            'Authorization': `Token ${this.config.deepgramApiKey}`,
            'Content-Type': 'audio/wav'
          },
          params: {
            model: 'nova-2',
            language: this.config.language || 'en',
            smart_format: true,
            punctuate: true,
            diarize: false
          },
          timeout: 10000
        }
      );

      const result = response.data;
      const transcript = result.results?.channels?.[0]?.alternatives?.[0];

      if (transcript && transcript.transcript.trim()) {
        return {
          text: transcript.transcript.trim(),
          confidence: transcript.confidence || 0.8,
          language: this.config.language || 'en'
        };
      }

      return null;

    } catch (error) {
      logger.error('❌ Deepgram STT error:', error);
      return null;
    }
  }

  // ElevenLabs STT implementation
  private async elevenlabsSTT(audioBuffer: Buffer): Promise<TranscriptionResult | null> {
    if (!this.config.elevenlabsApiKey) {
      throw new Error('ElevenLabs API key not configured');
    }
    try {
      // Use audio buffer directly (assuming it's already in correct format)
      const wavBuffer = audioBuffer;

      // ElevenLabs STT requires form data with file parameter
      const formData = new FormData();
      formData.append('file', wavBuffer, {
        filename: 'audio.wav',
        contentType: 'audio/wav'
      });

      // Use scribe_v1 model for speech-to-text
      formData.append('model_id', 'scribe_v1');

      // Add optional parameters for better transcription
      formData.append('language_code', 'en');
      formData.append('enable_logging', 'true');

      console.log('🎤 Sending audio to ElevenLabs STT, size:', wavBuffer.length, 'bytes');
      console.log('🎤 Audio format check - first 20 bytes:', wavBuffer.slice(0, 20).toString('hex'));

      const response = await axios.post(
        'https://api.elevenlabs.io/v1/speech-to-text',
        formData,
        {
          headers: {
            'xi-api-key': this.config.elevenlabsApiKey,
            ...formData.getHeaders()
          },
          timeout: 15000
        }
      );

      const data = response.data;
      console.log('🎤 ElevenLabs STT response:', data);

      if (data.text && data.text.trim()) {
        return {
          text: data.text.trim(),
          confidence: data.confidence || 0.8,
          language: this.config.language || 'en'
        };
      }
      return null;
    } catch (error: any) {
      console.error('❌ ElevenLabs STT error details:', {
        message: error?.message,
        status: error?.response?.status,
        data: error?.response?.data
      });
      logger.error('❌ ElevenLabs STT error:', error);
      return null;
    }
  }
  
  // Mock STT for testing
  private async mockSTT(audioBuffer: Buffer): Promise<TranscriptionResult | null> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 200));

    // Mock responses based on audio length
    const duration = audioBuffer.length / (8000 * 2); // Assuming 16-bit PCM at 8kHz
    
    if (duration < 0.5) {
      return null; // Too short to be meaningful speech
    }

    const mockResponses = [
      "I'm interested in your services",
      "Can you tell me about pricing?",
      "I'd like to schedule a meeting",
      "What are your business hours?",
      "How does this work?",
      "Yes, that sounds good",
      "No, I'm not interested",
      "Can you repeat that?",
      "I need to think about it",
      "Thank you for calling"
    ];

    const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)] || "Hello, how can I help you?";

    return {
      text: randomResponse,
      confidence: 0.85,
      language: 'en',
      duration
    };
  }

  // OpenAI TTS
  private async openaiTTS(text: string): Promise<Buffer> {
    if (!this.config.openaiApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      const response = await axios.post(
        'https://api.openai.com/v1/audio/speech',
        {
          model: 'tts-1',
          input: text,
          voice: this.config.voice || 'alloy',
          response_format: 'wav'
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.openaiApiKey}`,
            'Content-Type': 'application/json'
          },
          responseType: 'arraybuffer',
          timeout: 15000
        }
      );

      // Convert WAV to PCM for Exotel
      const wavBuffer = Buffer.from(response.data);
      return this.wavToPcm(wavBuffer);

    } catch (error) {
      logger.error('❌ OpenAI TTS error:', error);
      throw error;
    }
  }

  // ElevenLabs TTS
  private async elevenlabsTTS(text: string): Promise<Buffer> {
    if (!this.config.elevenlabsApiKey) {
      throw new Error('ElevenLabs API key not configured');
    }

    try {
      const voiceId = this.config.voice || 'pNInz6obpgDQGcFmaJgB'; // Default voice

      const response = await axios.post(
        `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`,
        {
          text,
          model_id: 'eleven_monolingual_v1',
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.5
          }
        },
        {
          headers: {
            'Accept': 'audio/mpeg',
            'Content-Type': 'application/json',
            'xi-api-key': this.config.elevenlabsApiKey
          },
          responseType: 'arraybuffer',
          timeout: 15000
        }
      );

      // Convert MP3 to PCM (would need audio conversion library in production)
      // For now, return the raw audio and handle conversion elsewhere
      return Buffer.from(response.data);

    } catch (error) {
      logger.error('❌ ElevenLabs TTS error:', error);
      throw error;
    }
  }

  // Mock TTS for testing
  private async mockTTS(text: string): Promise<Buffer> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Generate mock audio based on text length
    const duration = Math.max(1000, text.length * 50); // ~50ms per character
    const pcmData = this.generateSilence(duration);

    // Convert PCM to WAV format for proper playback
    return this.pcmToWav(pcmData);
  }

  // Audio format conversion utilities
  private pcmToWav(pcmBuffer: Buffer): Buffer {
    // Simple WAV header for 16-bit PCM, 8kHz, mono
    const sampleRate = 8000;
    const numChannels = 1;
    const bitsPerSample = 16;
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const dataSize = pcmBuffer.length;
    const fileSize = 36 + dataSize;

    const header = Buffer.alloc(44);
    let offset = 0;

    // RIFF header
    header.write('RIFF', offset); offset += 4;
    header.writeUInt32LE(fileSize, offset); offset += 4;
    header.write('WAVE', offset); offset += 4;

    // fmt chunk
    header.write('fmt ', offset); offset += 4;
    header.writeUInt32LE(16, offset); offset += 4; // chunk size
    header.writeUInt16LE(1, offset); offset += 2; // audio format (PCM)
    header.writeUInt16LE(numChannels, offset); offset += 2;
    header.writeUInt32LE(sampleRate, offset); offset += 4;
    header.writeUInt32LE(byteRate, offset); offset += 4;
    header.writeUInt16LE(blockAlign, offset); offset += 2;
    header.writeUInt16LE(bitsPerSample, offset); offset += 2;

    // data chunk
    header.write('data', offset); offset += 4;
    header.writeUInt32LE(dataSize, offset);

    return Buffer.concat([header, pcmBuffer]);
  }

  private wavToPcm(wavBuffer: Buffer): Buffer {
    // Skip WAV header (44 bytes) and return PCM data
    return wavBuffer.slice(44);
  }

  private generateSilence(durationMs: number): Buffer {
    // Generate silence for 16-bit PCM at 8kHz
    const sampleRate = 8000;
    const samples = Math.floor(sampleRate * durationMs / 1000);
    return Buffer.alloc(samples * 2); // 2 bytes per sample for 16-bit
  }

  // 🔥 BEST PRACTICE: Ensure audio chunks follow Exotel's recommendations
  // 100ms PCM chunks with 3200 bytes of raw audio (multiple of 320 bytes)
  validateChunkSize(audioBuffer: Buffer): boolean {
    const expectedSize = 3200; // 100ms at 8kHz, 16-bit, mono = 800 samples * 2 bytes * 2 (safety factor)
    const isMultipleOf320 = audioBuffer.length % 320 === 0;
    const isWithinRange = audioBuffer.length >= 3200 && audioBuffer.length <= 100000;

    if (!isMultipleOf320) {
      logger.warn(`⚠️ Audio chunk size ${audioBuffer.length} is not a multiple of 320 bytes`);
    }

    if (!isWithinRange) {
      logger.warn(`⚠️ Audio chunk size ${audioBuffer.length} is outside recommended range (*********** bytes)`);
    }

    return isMultipleOf320 && isWithinRange;
  }

  // Ensure outgoing audio chunks meet Exotel requirements
  public prepareAudioForExotel(audioBuffer: Buffer): Buffer {
    // Ensure chunk is multiple of 320 bytes
    const remainder = audioBuffer.length % 320;
    if (remainder !== 0) {
      const paddingSize = 320 - remainder;
      const padding = Buffer.alloc(paddingSize);
      return Buffer.concat([audioBuffer, padding]);
    }
    return audioBuffer;
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      // Test with a small silence buffer
      const testAudio = this.generateSilence(100);
      
      // Skip STT health check to avoid false positives with silence
      // if (this.config.sttProvider !== 'mock') {
      //   await this.speechToText(testAudio);
      // }
      
      if (this.config.ttsProvider !== 'mock') {
        await this.textToSpeech('Test');
      }
      
      return true;
    } catch (error) {
      logger.error('❌ Audio processor health check failed:', error);
      return false;
    }
  }
}
